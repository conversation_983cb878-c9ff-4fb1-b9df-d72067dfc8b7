import os
import sys
import psycopg2
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv('toolsua-backend/.env')

# Obtener la URL de la base de datos
db_url = os.getenv('DATABASE_URL')
print(f"URL de la base de datos: {db_url}")

try:
    # Intentar conectar a la base de datos
    conn = psycopg2.connect(db_url)
    print("Conexión exitosa a la base de datos PostgreSQL")
    
    # Crear un cursor
    cursor = conn.cursor()
    
    # Ejecutar una consulta simple
    cursor.execute("SELECT version();")
    
    # Obtener el resultado
    db_version = cursor.fetchone()
    print(f"Versión de PostgreSQL: {db_version[0]}")
    
    # Listar las tablas en la base de datos
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name;
    """)
    
    tables = cursor.fetchall()
    print("\nTablas en la base de datos:")
    for table in tables:
        print(f"- {table[0]}")
    
    # Cerrar el cursor y la conexión
    cursor.close()
    conn.close()
    print("\nConexión cerrada")
    
except Exception as e:
    print(f"Error al conectar a la base de datos: {e}")
