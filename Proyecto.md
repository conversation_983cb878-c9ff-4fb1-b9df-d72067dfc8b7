# Documentación Completa del Proyecto ToolSUA-V2

# Índice de Archivos y Funcionalidades

## Archivos Principales
- **carga_masiva_movimientos.py**: Interfaz principal para carga de movimientos de todo tipo (bajas, reingresos, incapacidades, créditos).
- **carga_masiva_asegurados.py**: Interfaz para carga masiva de trabajadores.
- **carga_masiva_patrones.py**: Interfaz para carga masiva de registros patronales.
- **actualizar_fraccion_patrones.py**: Herramienta para actualizar fracciones y primas RT.

## Conectores de Base de Datos
- **conectores/conector_sua.py**: Clase principal para interacción con la base de datos SUA.
- **conectores/__init__.py**: Inicializador del paquete de conectores.

## Scripts de Prueba
- **test_conexion.py**: Verifica la conectividad a la base de datos.
- **test_baja.py** / **test_baja_fix.py**: Pruebas para movimientos de baja.
- **test_reinicio.py**: Pruebas para reinicio de créditos Infonavit.
- **probar_reingreso.py**: Pruebas para reingresos de trabajadores.
- **crear_archivo_test.py** / **crear_archivo_test_creditos.py**: Generación de archivos de prueba.

## Scripts de Análisis
- **explorar_bd_sua.py**: Examina la estructura de la base de datos SUA.
- **explorar_tablas_principales.py**: Analiza las tablas principales.
- **examinar_bajas.py**: Examina movimientos de baja existentes.
- **explorar_tabla_movtos.py**: Analiza tabla de movimientos.
- **explorar_tabla_patron.py**: Analiza tabla de patrones.

## Utilidades
- **verificar_credito.py**: Verifica el estado de créditos Infonavit.
- **ver_datos_asegurado.py**: Muestra información detallada de asegurados.
- **verificar_campos_asegura.py**: Verifica estructura de tabla Asegura.
- **listar_columnas.py**: Muestra columnas de las tablas.
- **corregir_asegurado.py**: Corrige datos de asegurados.
- **limpiar_pruebas.py**: Elimina registros de prueba.

## Modelos
- **modelos/patron.py**: Clase para manejo de patrones.
- **modelos/prima_rt.py**: Clase para manejo de primas RT.

## Documentación
- **README_movimientos.md**: Documentación de movimientos.
- **README_carga_masiva.md**: Documentación de carga masiva.
- **README-desarrollo.md**: Guía para desarrolladores.
- **README.md**: Información general del proyecto.
- **DOCUMENTACION_PROYECTO.md**: Este archivo (documentación completa).

## Visión General

**ToolSUA** es una aplicación diseñada para extender y mejorar las capacidades del Sistema Único de Autodeterminación (SUA) del IMSS. La herramienta permite a contadores, administradores y profesionales de recursos humanos realizar operaciones masivas, generar reportes personalizados y automatizar procesos que serían manuales y tediosos en el sistema oficial.

La aplicación opera principalmente con la base de datos Access (SUA.MDB) que utiliza el sistema oficial, respetando su estructura pero añadiendo funcionalidades avanzadas que facilitan la gestión de grandes volúmenes de datos.

### Objetivos del Proyecto

1. Proporcionar herramientas para carga masiva de información (patrones, trabajadores y movimientos)
2. Facilitar la manipulación de créditos Infonavit dentro del sistema SUA
3. Automatizar operaciones complejas o repetitivas
4. Mantener la compatibilidad total con el sistema SUA oficial
5. Ofrecer validaciones avanzadas para evitar errores en datos críticos

## Arquitectura del Sistema

### Componentes Principales

1. **Conectores a Base de Datos**: Módulos para interactuar con la base de datos SUA.MDB
2. **Procesadores de Datos**: Clases para manejar la lógica de negocio
3. **Interfaces de Usuario**: Interfaces gráficas para las diferentes funcionalidades
4. **Validadores**: Sistemas de validación para garantizar la integridad de los datos

### Tecnologías Utilizadas

- **Python**: Lenguaje principal de desarrollo
- **PyODBC**: Para la conexión con la base de datos Access
- **Pandas**: Para procesamiento y manipulación de datos
- **Tkinter**: Para la interfaz gráfica de usuario
- **Openpyxl**: Para manejo de archivos Excel

## Estructura de la Base de Datos SUA

El sistema SUA utiliza una base de datos Microsoft Access con múltiples tablas relacionadas. Las principales tablas son:

### Tabla Patron

Almacena la información de los registros patronales.

**Campos clave**:
- **REG_PAT**: Registro patronal (clave primaria)
- **RFC_PAT**: RFC del patrón
- **NOM_PAT**: Nombre o razón social
- **Prima_RT**: Prima de riesgo de trabajo

### Tabla Asegura

Contiene los datos principales de los trabajadores asegurados.

**Campos clave**:
- **REG_PATR**: Registro patronal
- **NUM_AFIL**: Número de afiliación (NSS)
- **FEC_ALT**: Fecha de alta
- **FEC_BAJ**: Fecha de baja (si existe)
- **SAL_IMSS**: Salario registrado ante el IMSS
- **TIP_DSC**: Tipo de descuento Infonavit (1=Porcentaje, 2=Cuota Fija, 3=Factor de Descuento)
- **VAL_DSC**: Valor del descuento Infonavit
- **FEC_DSC**: Fecha del inicio del descuento Infonavit
- **Num_Cre**: Número de crédito Infonavit

### Tabla Movtos

Registra todos los movimientos relacionados con los trabajadores.

**Campos clave**:
- **REG_PATR**: Registro patronal
- **NUM_AFIL**: Número de afiliación
- **TIP_MOVS**: Tipo de movimiento:
  - 01: Alta
  - 02: Baja
  - 07: Modificación de salario
  - 08: Reingreso
  - 11: Ausentismo
  - 12: Incapacidad
  - 15: Inicio de crédito Infonavit
  - 16: Suspensión de crédito Infonavit
  - 17: Reinicio de crédito Infonavit
  - 18: Modificación de tipo de descuento
  - 19: Modificación de valor de descuento
  - 20: Modificación de número de crédito
- **FEC_INIC**: Fecha del movimiento
- **CVE_MOVS**: Clave del movimiento:
  - A: Alta
  - G: Baja/Suspensión de crédito
  - D: Inicio/Reinicio de crédito
  - R: Reingreso
  - C: Ausentismo
- **SAL_MOVT**: Salario para el movimiento
- **SAL_MOVT2**: Salario complementario 2
- **SAL_MOVT3**: Salario complementario 3
- **SAL_ANT1**: Salario anterior 1
- **SAL_ANT2**: Salario anterior 2
- **SAL_ANT3**: Salario anterior 3
- **Num_Cre**: Número de crédito Infonavit
- **Tip_Des**: Tipo de descuento (formato texto: "Porcentaje", "Cuota Fija", "Factor de Descuento")
- **Val_Des**: Valor del descuento
- **Tab_Dism**: Tabla de disminución

## Documentación de Scripts Principales

### Conectores y Clases Base

#### `conectores/conector_sua.py`
Clase principal de conexión y manipulación de la base de datos SUA. Contiene todos los métodos necesarios para interactuar con las tablas del sistema.

**Funciones principales**:
- `conectar()`: Establece conexión con la base de datos SUA
- `desconectar()`: Cierra la conexión a la base de datos
- `listar_tablas()`: Obtiene la lista de todas las tablas en la base de datos
- `obtener_estructura_tabla()`: Devuelve la estructura de columnas de una tabla
- `obtener_datos_tabla()`: Obtiene registros de una tabla con filtros opcionales
- `ejecutar_consulta()`: Ejecuta una consulta SQL personalizada
- `obtener_metadata_tabla()`: Obtiene metadatos detallados sobre las columnas de una tabla
- `existe_tabla()`: Verifica si existe una tabla con el nombre dado
- `insertar_movimiento()`: Inserta un movimiento genérico en la tabla Movtos
- `procesar_movimiento()`: Procesa cualquier tipo de movimiento según su código
- `procesar_baja()`: Procesa una baja (tipo 02)
- `procesar_reingreso()`: Procesa un reingreso (tipo 08)
- `procesar_credito_infonavit()`: Procesa inicio de crédito Infonavit (tipo 15)
- `procesar_ausencia()`: Procesa ausencias (tipo 11)
- `procesar_modificacion_salario()`: Procesa modificación de salario (tipo 07)
- `procesar_modificacion_tipo_descuento()`: Procesa modificación de tipo de descuento Infonavit (tipo 18)
- `procesar_suspension_credito()`: Procesa suspensión de crédito Infonavit (tipo 16)
- `procesar_reinicio_credito()`: Procesa reinicio de crédito Infonavit (tipo 17)

### Interfaces de Usuario y Scripts Principales

#### `carga_masiva_movimientos.py`
Interfaz gráfica para la carga masiva de movimientos desde archivos Excel.

**Funciones principales**:
- `cargar_archivo()`: Selecciona y carga un archivo Excel
- `validar_excel()`: Verifica la estructura y contenido del archivo
- `cargar_movimientos()`: Procesa todos los movimientos del archivo
- `generar_plantilla()`: Crea una plantilla Excel con la estructura correcta
- `analizar_movimientos()`: Muestra ejemplos reales de cada tipo de movimiento
- `mostrar_log()`: Visualiza y guarda registros de las operaciones realizadas
- `validar_movimiento()`: Valida un movimiento antes de procesarlo
- `procesar_fila()`: Procesa una fila del Excel como un movimiento
- `guardar_log()`: Guarda el registro de operaciones en un archivo
- `procesar_incapacidad()`: Procesa incapacidades (tipo 12) con soporte para diferentes ramas de seguro
- `dividir_incapacidad_por_mes()`: Divide incapacidades largas en registros mensuales
- `validar_combinacion_incapacidad()`: Valida la combinación de rama, tipo y consecuencia

#### `carga_masiva_asegurados.py`
Interfaz para importar múltiples asegurados, con soporte para créditos Infonavit.

**Funciones principales**:
- `procesar_asegurados_excel()`: Procesa todos los asegurados del archivo Excel
- `cargar_asegurado()`: Inserta un asegurado completo en las tablas necesarias
- `validar_registro_patronal()`: Verifica que el registro patronal exista
- `procesar_credito_infonavit()`: Procesa la información de crédito Infonavit si existe
- `convertir_tipo_descuento()`: Maneja la conversión entre formatos de descuento
- `generar_plantilla()`: Crea una plantilla Excel para carga de asegurados
- `validar_excel()`: Verifica la estructura del archivo Excel
- `mostrar_log()`: Visualiza los resultados del proceso de carga

#### `carga_masiva_patrones.py`
Interfaz para la carga masiva de registros patronales con sus primas RT.

**Funciones principales**:
- `cargar_patrones()`: Procesa todos los patrones del archivo Excel
- `validar_patron()`: Verifica que los datos del patrón sean correctos
- `insertar_patron()`: Inserta un patrón en la tabla Patron
- `insertar_prima_rt()`: Registra la prima de riesgo de trabajo
- `buscar_id_entidad()`: Busca el ID correspondiente a una entidad federativa
- `buscar_id_subdelegacion()`: Busca el ID correspondiente a una subdelegación
- `generar_plantilla()`: Crea una plantilla Excel para carga de patrones
- `validar_excel()`: Verifica la estructura del archivo Excel
- `validar_campos_obligatorios()`: Verifica que estén presentes todos los campos requeridos

#### `actualizar_fraccion_patrones.py`
Script para actualizar la fracción y prima RT de patrones existentes.

**Funciones principales**:
- `actualizar_fracciones()`: Actualiza las fracciones de múltiples patrones
- `obtener_patrones()`: Lista los patrones existentes en la base de datos
- `validar_fraccion()`: Verifica que la fracción sea válida según la clase
- `calcular_prima_rt()`: Calcula la prima de riesgo de trabajo según la fracción
- `generar_plantilla()`: Crea una plantilla Excel para actualización de fracciones
- `validar_excel()`: Verifica la estructura del archivo Excel
- `cargar_archivo()`: Carga un archivo Excel para su procesamiento

### Scripts de Exploración y Análisis

#### `explorar_bd_sua.py`
Script para explorar la estructura de la base de datos SUA y extraer información.

**Funciones principales**:
- `explorar_base_datos()`: Lista todas las tablas de la base de datos
- `explorar_tabla()`: Muestra la estructura de una tabla específica
- `obtener_muestra_datos()`: Extrae una muestra de datos de una tabla
- `guardar_resultados_csv()`: Guarda los resultados en archivos CSV
- `mostrar_relaciones()`: Intenta identificar relaciones entre tablas
- `contar_registros()`: Cuenta los registros en cada tabla

#### `explorar_tablas_principales.py`
Analiza en detalle las tablas principales de SUA y sus relaciones.

**Funciones principales**:
- `analizar_tabla_asegura()`: Analiza la estructura y contenido de la tabla Asegura
- `analizar_tabla_movtos()`: Analiza la estructura y contenido de la tabla Movtos
- `obtener_tipos_movimientos()`: Lista los tipos de movimientos existentes
- `obtener_relaciones_tablas()`: Muestra las relaciones entre tablas
- `analizar_salarios()`: Analiza los campos de salario en diferentes tablas
- `mostrar_registros_muestra()`: Muestra registros de muestra de cada tabla

#### `examinar_bajas.py`
Script especializado en analizar movimientos de baja y su procesamiento.

**Funciones principales**:
- `analizar_bajas()`: Analiza los movimientos de baja existentes
- `verificar_secuencia_bajas()`: Verifica el contador secuencial de bajas
- `analizar_estructura_bajas()`: Examina la estructura de los datos de baja
- `buscar_inconsistencias()`: Busca inconsistencias en los registros de baja
- `generar_reporte()`: Genera un reporte detallado de análisis
- `mostrar_estadisticas()`: Muestra estadísticas sobre las bajas registradas

#### `explorar_tabla_movtos.py`
Analiza en detalle la estructura y contenido de la tabla Movtos.

**Funciones principales**:
- `analizar_estructura()`: Analiza la estructura de la tabla Movtos
- `obtener_estadisticas_tipos()`: Obtiene estadísticas sobre tipos de movimientos
- `analizar_movimiento_tipo()`: Analiza en detalle un tipo específico de movimiento
- `exportar_analisis()`: Exporta el análisis a un archivo CSV
- `visualizar_distribucion()`: Visualiza la distribución de tipos de movimientos

#### `explorar_tabla_patron.py`
Analiza en detalle la estructura y contenido de la tabla Patron.

**Funciones principales**:
- `analizar_estructura()`: Analiza la estructura de la tabla Patron
- `obtener_estadisticas_patrones()`: Obtiene estadísticas sobre registros patronales
- `analizar_primas_rt()`: Analiza las primas de riesgo de trabajo
- `exportar_lista_patrones()`: Exporta la lista de patrones a un archivo CSV
- `buscar_patron()`: Busca información detallada de un patrón específico

### Scripts de Prueba y Validación

#### `test_conexion.py`
Prueba la conexión a la base de datos y funcionalidades básicas.

**Funciones principales**:
- `probar_conexion()`: Verifica la conexión a la base de datos
- `probar_consulta_basica()`: Prueba una consulta simple
- `verificar_driver()`: Comprueba la disponibilidad del driver ODBC
- `probar_listado_tablas()`: Prueba el listado de tablas
- `mostrar_metadatos()`: Muestra metadatos de tablas principales

#### `test_baja.py` / `test_baja_fix.py`
Scripts para probar el procesamiento de bajas y su correcto funcionamiento.

**Funciones principales**:
- `probar_baja_simple()`: Prueba una baja sin crédito Infonavit
- `probar_baja_con_credito()`: Prueba una baja con crédito Infonavit activo
- `verificar_contador_bajas()`: Verifica el contador secuencial de bajas
- `comprobar_actualizacion_asegura()`: Verifica que la tabla Asegura se actualice correctamente
- `comprobar_creacion_movimiento_tipo16()`: Verifica la creación del movimiento tipo 16 en bajas con crédito

#### `test_reinicio.py`
Script para probar el procesamiento de reinicio de créditos Infonavit.

**Funciones principales**:
- `probar_reinicio_credito()`: Prueba un reinicio de crédito completo
- `verificar_datos_reinicio()`: Verifica que los datos se hayan actualizado correctamente
- `probar_validaciones()`: Prueba las validaciones en el proceso de reinicio
- `comprobar_valores_campos()`: Verifica que los campos tengan los valores correctos
- `analizar_movimiento_creado()`: Analiza en detalle el movimiento creado

#### `probar_reingreso.py`
Prueba el procesamiento de reingresos de trabajadores.

**Funciones principales**:
- `probar_reingreso_simple()`: Prueba un reingreso básico
- `probar_reingreso_con_credito()`: Prueba un reingreso con crédito Infonavit
- `verificar_estado_asegura()`: Verifica el estado en Asegura después del reingreso
- `analizar_movimiento_reingreso()`: Analiza el movimiento de reingreso creado
- `probar_validaciones_reingreso()`: Prueba las validaciones del reingreso

### Scripts de Utilidad y Generación de Datos

#### `crear_archivo_test.py` / `crear_archivo_test_creditos.py`
Scripts para generar archivos de prueba con datos de muestra.

**Funciones principales**:
- `crear_excel_prueba()`: Crea un archivo Excel con datos aleatorios para pruebas
- `generar_datos_aleatorios()`: Genera datos aleatorios válidos para pruebas
- `generar_nss_valido()`: Genera un número de seguridad social válido
- `generar_registro_patronal()`: Genera un registro patronal válido
- `generar_fechas_coherentes()`: Genera fechas que cumplen las reglas de negocio

#### `crear_excel_reinicio.py`
Script para crear un archivo Excel de prueba para movimientos de reinicio de crédito.

**Funciones principales**:
- `crear_excel_reinicio()`: Crea un Excel con datos para probar reinicio de créditos
- `obtener_suspensiones_activas()`: Obtiene trabajadores con suspensiones activas
- `generar_fechas_validas()`: Genera fechas válidas para reinicio
- `guardar_excel()`: Guarda los datos en un archivo Excel

#### `crear_excel_asegurados_prueba.py`
Script para crear un archivo Excel de prueba para la carga de asegurados.

**Funciones principales**:
- `crear_excel_asegurados()`: Crea un Excel con datos de asegurados de prueba
- `generar_datos_personales()`: Genera datos personales aleatorios
- `generar_datos_infonavit()`: Genera datos de crédito Infonavit aleatorios
- `obtener_patrones_validos()`: Obtiene patrones válidos de la base de datos

#### `corregir_asegurado.py`
Script para corregir problemas específicos en registros de asegurados.

**Funciones principales**:
- `corregir_registro()`: Corrige problemas en un registro específico
- `detectar_problemas()`: Identifica asegurados con problemas en sus datos
- `actualizar_datos()`: Actualiza los datos de un asegurado con información corregida
- `corregir_credito_infonavit()`: Corrige problemas en los datos de crédito Infonavit
- `generar_reporte_correcciones()`: Genera un reporte de las correcciones realizadas

#### `limpiar_pruebas.py`
Elimina registros de prueba de la base de datos.

**Funciones principales**:
- `eliminar_asegurado()`: Elimina un asegurado por su NSS y registro patronal
- `eliminar_movimientos()`: Elimina los movimientos asociados a un asegurado
- `limpiar_por_patron()`: Elimina todos los asegurados de un patrón específico
- `buscar_registros_prueba()`: Busca registros que parecen ser de prueba
- `hacer_respaldo()`: Hace un respaldo antes de eliminar datos

#### `verificar_credito.py`
Verifica el estado de los créditos Infonavit en la base de datos.

**Funciones principales**:
- `verificar_credito_asegurado()`: Verifica el estado del crédito de un asegurado
- `listar_asegurados_con_credito()`: Lista todos los asegurados con crédito activo
- `buscar_inconsistencias_credito()`: Busca inconsistencias en los datos de crédito
- `verificar_fechas_credito()`: Verifica la coherencia de las fechas de crédito
- `exportar_reporte()`: Exporta un reporte detallado de los créditos

#### `ver_datos_asegurado.py`
Muestra información detallada de un asegurado específico.

**Funciones principales**:
- `obtener_datos_completos()`: Obtiene todos los datos disponibles de un asegurado
- `mostrar_movimientos()`: Muestra los movimientos asociados al asegurado
- `analizar_historial()`: Analiza el historial completo del asegurado
- `verificar_estado_actual()`: Verifica el estado actual del asegurado
- `exportar_historial()`: Exporta el historial completo a un archivo

#### `verificar_campos_asegura.py`
Verifica la estructura y contenido de los campos en la tabla Asegura.

**Funciones principales**:
- `analizar_campos()`: Analiza la estructura y tipos de datos de los campos
- `verificar_integridad()`: Verifica la integridad de los datos
- `buscar_valores_nulos()`: Identifica campos con valores nulos
- `analizar_distribuciones()`: Analiza la distribución de valores en campos clave
- `generar_reporte()`: Genera un reporte detallado del análisis

### Scripts de Consulta y Análisis Ad-hoc

#### `consulta_asegura.py`
Permite realizar consultas personalizadas a la tabla Asegura.

**Funciones principales**:
- `consultar_por_nss()`: Consulta un asegurado por su número de seguridad social
- `consultar_por_patron()`: Consulta asegurados de un patrón específico
- `consultar_por_fecha()`: Consulta asegurados por fecha de alta o baja
- `exportar_resultados()`: Exporta los resultados de la consulta
- `mostrar_estadisticas()`: Muestra estadísticas de los resultados obtenidos

#### `consultar_patron.py`
Permite consultar información detallada de registros patronales.

**Funciones principales**:
- `obtener_datos_patron()`: Obtiene todos los datos de un patrón específico
- `listar_trabajadores()`: Lista los trabajadores asociados al patrón
- `obtener_prima_rt()`: Obtiene la prima de riesgo de trabajo
- `analizar_movimientos_patron()`: Analiza los movimientos asociados al patrón
- `exportar_datos_patron()`: Exporta los datos del patrón a un archivo

#### `listar_columnas.py`
Utilitario para listar las columnas de cualquier tabla en la base de datos.

**Funciones principales**:
- `listar_columnas_tabla()`: Lista las columnas de una tabla específica
- `mostrar_tipos_datos()`: Muestra los tipos de datos de cada columna
- `buscar_columna()`: Busca una columna en todas las tablas
- `comparar_tablas()`: Compara la estructura de dos tablas
- `generar_referencia()`: Genera una referencia completa de la base de datos

### Modelos y Clases de Dominio

#### `modelos/patron.py`
Clase para manejar la lógica de negocio relacionada con los patrones.

**Funciones principales**:
- `validar()`: Valida que los datos del patrón sean correctos
- `formatear_registro_patronal()`: Da formato adecuado al registro patronal
- `obtener_subdelegacion()`: Obtiene la subdelegación a partir del registro patronal
- `calcular_digito_verificador()`: Calcula el dígito verificador del registro patronal
- `convertir_a_dict()`: Convierte el objeto a un diccionario para persistencia

#### `modelos/prima_rt.py`
Clase para manejar la lógica relacionada con primas de riesgo de trabajo.

**Funciones principales**:
- `validar_fraccion()`: Valida que la fracción sea válida
- `calcular_prima()`: Calcula la prima RT a partir de la fracción y clase
- `obtener_clase_actividad()`: Obtiene la clase de actividad a partir de la fracción
- `validar_rango()`: Valida que la prima esté en el rango correcto
- `convertir_a_dict()`: Convierte el objeto a un diccionario para persistencia

### Futuras Implementaciones de Scripts

Como parte del plan de desarrollo, se implementarán los siguientes scripts:

#### `procesar_modificacion_tipo_descuento.py`
Procesará movimientos tipo 18 (Modificación de Tipo de Descuento).

#### `procesar_modificacion_valor_descuento.py`
Procesará movimientos tipo 19 (Modificación de Valor de Descuento Infonavit).

#### `procesar_modificacion_numero_credito.py`
Procesará movimientos tipo 20 (Modificación de Número de Crédito Infonavit).

#### `procesar_aportacion_voluntaria.py`
Procesará movimientos tipo 09 (Aportación Voluntaria).

## Desarrollo Futuro y Mejoras Pendientes

### Movimientos Pendientes por Implementar

#### Procesamiento de Tipos de Movimiento Adicionales

1. **Movimientos de Crédito Infonavit**:
   - **Tipo 19 (Modificación de Valor de Descuento)**: Actualiza el valor del descuento manteniendo el mismo tipo.
   - **Tipo 20 (Modificación de Número de Crédito)**: Corrige o actualiza el número de crédito Infonavit.

2. **Movimientos de Modificación de Datos del Trabajador**:
   - **Tipo 09 (Aportación Voluntaria)**: Registro de aportaciones voluntarias al IMSS.
   - **Tipo 14 (Cambio de Nombre)**: Actualización del nombre del asegurado.
   - **Tipo 13 (Cambio de NSS)**: Corrección del Número de Seguridad Social.

#### Movimientos Implementados Recientemente

1. **Movimientos de Crédito Infonavit**:
   - **Tipo 16 (Suspensión de Crédito)**: Suspende temporalmente el descuento del crédito Infonavit.
   - **Tipo 17 (Reinicio de Crédito)**: Reinicia un crédito previamente suspendido.
   - **Tipo 18 (Modificación de Tipo de Descuento)**: Permite cambiar entre Porcentaje, Cuota Fija y Factor de Descuento.

### Nota sobre Incapacidades (Tipo 12)

Los movimientos tipo 12 (Incapacidad) ya están implementados en el archivo `carga_masiva_movimientos.py` con las siguientes características:

- Soporte para las tres ramas de seguro:
  - Riesgos de Trabajo (1)
  - Enfermedad General (2) 
  - Maternidad (3)
  
- Manejo de todos los tipos de control:
  - Única (1)
  - Inicial (2)
  - Subsecuente (3)
  - Alta Médica RT (4)
  - Valuación (5)
  - Recaída (6)
  - Prenatal (7)
  - Enlace (8)
  
- División automática de incapacidades que abarcan más de un mes
- Validación de combinaciones válidas de rama, tipo y consecuencia
- Validaciones de solapamiento con otras incapacidades
- Soporte para folios y porcentajes de incapacidad

La interfaz también proporciona plantillas con ejemplos para facilitar la carga de estos movimientos.

### Implementación de Movimiento Tipo 18 (Modificación de Tipo de Descuento)

Se ha completado la implementación del movimiento tipo 18 (Modificación de Tipo de Descuento) en el archivo `conectores/conector_sua.py` con las siguientes características:

1. **Procesamiento del Movimiento**:
   - Se realiza la validación de campos obligatorios: registro patronal, NSS, fecha, tipo de movimiento y tipo de descuento.
   - Se convierte el tipo de descuento al formato correcto, normalizando entre representaciones textuales y numéricas (1=Porcentaje, 2=Cuota Fija, 3=Factor de Descuento).
   - Se verifica que el asegurado exista y tenga un crédito activo antes de procesar el cambio.
   - Se genera un registro en la tabla Movtos con CVE_MOVS='M' y los campos necesarios.
   - Se actualiza el campo TIP_DSC en la tabla Asegura con el nuevo valor.
   - Se implementa manejo de errores con rollback de transacciones en caso de fallos.

2. **Seguridad Mejorada**:
   - Las consultas SQL utilizan parámetros con marcadores de posición (?) en lugar de interpolación de cadenas.
   - Esta implementación previene vulnerabilidades de inyección SQL, haciendo el código más seguro.
   - Se han implementado transacciones completas para garantizar la consistencia de datos.

3. **Diseño Modular**:
   - El método `procesar_movimiento` ha sido actualizado para actuar como dispatcher, delegando al método especializado correspondiente.
   - Este diseño facilita el mantenimiento y la incorporación de nuevos tipos de movimientos en el futuro.
   - La validación de campos comunes se realiza una sola vez en el dispatcher.

4. **Estructura del Registro**:
   - Los registros generados tienen la siguiente estructura principal:
   ```
   REG_PATR    NUM_AFIL      TIP_MOVS  FEC_INIC     CON_SEC  NUM_DIAS  SAL_MOVT  SAL_MOVT2  CVE_MOVS  Num_Cre     Val_Des  Tip_Des
   E5352621109  04088625456  18        01/03/2025            0         $0.00     $0.00      M         1565544777  $6.59    Factor de Descuento
   ```

Esta implementación robusta permite a los usuarios modificar el tipo de descuento Infonavit de manera masiva y segura, facilitando la gestión de créditos dentro del sistema SUA y protegiendo la integridad de la base de datos.

### Mejoras en la Arquitectura del Sistema

1. **Optimización del Conector a Base de Datos**:
   - Implementación de patrón singleton para evitar múltiples conexiones.
   - Manejo de pool de conexiones para mejorar rendimiento en operaciones masivas.
   - Sistema de caché para consultas frecuentes.

2. **Refactorización de Código**:
   - Separación más clara de responsabilidades utilizando patrones de diseño.
   - Implementación de interfaces coherentes para todos los procesadores de movimientos.
   - Creación de clases abstractas para estandarizar el comportamiento de los procesadores.

3. **Mejoras en el Manejo de Errores**:
   - Sistema centralizado de logging con niveles de severidad.
   - Recuperación inteligente ante fallos en la base de datos.
   - Validaciones previas más exhaustivas para prevenir errores.

### Enriquecimiento de la Interfaz de Usuario

1. **Panel de Control Unificado**:
   - Interfaz centralizada para todas las operaciones del sistema.
   - Dashboard con estadísticas y estado de la base de datos.
   - Visualización de movimientos recientes y pendientes.

2. **Mejoras en la Experiencia de Usuario**:
   - Vista previa de datos antes de procesar operaciones masivas.
   - Indicadores de progreso para operaciones de larga duración.
   - Sistema de notificaciones para operaciones completadas.

3. **Herramientas de Reportería Avanzada**:
   - Generación de reportes personalizados según criterios del usuario.
   - Exportación en múltiples formatos (Excel, PDF, CSV).
   - Programación de reportes periódicos automatizados.

### Mejoras en Seguridad y Validación

1. **Sistema de Respaldos Automáticos**:
   - Respaldo automático antes de operaciones masivas.
   - Puntos de restauración con control de versiones.
   - Programación de respaldos periódicos configurables.

2. **Validaciones Avanzadas**:
   - Sistema de reglas de negocio configurable.
   - Validaciones cruzadas entre diferentes tablas.
   - Detección proactiva de inconsistencias en la base de datos.

3. **Auditoría de Operaciones**:
   - Registro detallado de todas las operaciones realizadas.
   - Trazabilidad completa de cambios en registros clave.
   - Informes de auditoría para control interno.

### Plan de Implementación a Corto Plazo

Para los próximos tres meses, se priorizará el desarrollo de las siguientes funcionalidades:

1. **Mes 1: Movimientos de Crédito Infonavit**
   - Implementación de procesadores para movimientos tipo 19 y 20.
   - Actualización de plantillas para incluir estos tipos.
   - Pruebas exhaustivas con datos reales.
   - *Logro completado*: Se ha implementado el procesamiento del movimiento tipo 18 (Modificación de Tipo de Descuento).

2. **Mes 2: Optimización y Mejoras de Rendimiento**
   - Refactorización del conector a base de datos.
   - Implementación de procesamiento por lotes.
   - Optimización de consultas SQL críticas.

3. **Mes 3: Interfaz Unificada y Reportería**
   - Desarrollo del panel de control centralizado.
   - Implementación de herramientas de reportes personalizados.
   - Sistema de respaldos automáticos.

### Implementaciones Recientes

1. **Movimiento Tipo 07 (Modificación de Salario)**:
   - Se ha implementado completamente el procesamiento del movimiento tipo 07.
   - Características principales:
     - Validación de campos obligatorios (REG_PATR, NUM_AFIL, FEC_INIC, TIP_MOVS, SAL_MOVT).
     - Verificación que el asegurado exista en la base de datos y esté activo.
     - Comparación entre el salario actual y el nuevo para evitar modificaciones innecesarias.
     - Actualización automática de la tabla Asegura con el nuevo salario.
     - Creación de registro en la tabla Movtos con CVE_MOVS='M'.
     - Manejo de valores históricos con SAL_ANT1 para mantener trazabilidad.
     - Validaciones completas y manejo de errores con mensajes detallados.

2. **Movimiento Tipo 18 (Modificación de Tipo de Descuento)**:
   - Se ha completado la implementación del movimiento tipo 18 con las siguientes características:
     - Validación de campos obligatorios: registro patronal, NSS, fecha, tipo de movimiento y tipo de descuento.
     - Conversión automática del tipo de descuento al formato requerido por el sistema.
     - Verificación que el asegurado tenga un crédito activo antes de procesar el cambio.
     - Creación de registro en la tabla Movtos con CVE_MOVS='M'.
     - Actualización correcta del campo TIP_DSC en la tabla Asegura.
     - Manejo de valores tanto numéricos como textuales para el tipo de descuento.
     - Validaciones de integridad y coherencia de datos.

### Dependencias Técnicas para el Desarrollo Futuro

Para implementar estas mejoras se requerirá:

1. **Bibliotecas Adicionales**:
   - SQLAlchemy para abstracción de base de datos y mejoras de rendimiento.
   - ReportLab para generación de reportes en PDF.
   - Matplotlib/Plotly para visualización de datos estadísticos.

2. **Actualizaciones de Infraestructura**:
   - Migración a Python 3.10+ para aprovechar mejoras de rendimiento.
   - Actualización de drivers ODBC a las versiones más recientes.
   - Implementación de pruebas automatizadas con pytest.

3. **Documentación Técnica**:
   - Documentación detallada de API para desarrolladores.
   - Guías de usuario para las nuevas funcionalidades.
   - Manuales de instalación y configuración actualizados.

## Requisitos para Contribuir al Proyecto

Para desarrolladores que deseen contribuir al proyecto, se requiere:

1. **Conocimientos Técnicos**:
   - Python intermedio-avanzado.
   - Experiencia con bases de datos Access y SQL.
   - Conocimiento del sistema SUA y normativas del IMSS/Infonavit.

2. **Entorno de Desarrollo**:
   - Python 3.8+ con entorno virtual.
   - Driver ODBC para Access configurado correctamente.
   - Editor con soporte para linting (VSCode recomendado).

3. **Proceso de Contribución**:
   - Fork del repositorio principal.
   - Desarrollo en ramas temáticas (feature branches).
   - Pull requests con descripción detallada de cambios.
   - Pruebas unitarias para cada nueva funcionalidad.

La implementación de estas mejoras permitirá que ToolSUA-V2 se convierta en una solución más robusta, completa y fácil de utilizar para la gestión de datos en el Sistema Único de Autodeterminación.

## Conclusión

ToolSUA-V2 es una solución robusta para la manipulación masiva de datos en el sistema SUA, facilitando operaciones que serían tediosas o complejas en la interfaz oficial. El sistema mantiene la compatibilidad total con el SUA y proporciona validaciones avanzadas para garantizar la integridad de los datos.

Las recientes mejoras en el procesamiento de movimientos de suspensión y reinicio de créditos Infonavit (tipos 16 y 17) han aumentado la fiabilidad del sistema, asegurando que los campos se inicialicen correctamente y los valores de CVE_MOVS sean adecuados.

El desarrollo futuro se enfocará en ampliar el soporte para otros tipos de movimientos y mejorar el rendimiento y la experiencia de usuario. 