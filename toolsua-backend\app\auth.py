from flask import Blueprint, request, jsonify
from app.models import User, Organization
from app.services.sua_migrator import SUAMigrator
from app import db
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
import pyodbc

bp = Blueprint('auth', __name__)

@bp.route('/register', methods=['POST'])
def register():
    data = request.get_json()

    if not all(k in data for k in ['username', 'email', 'password', 'organization_name', 'sua_db_path']):
        return jsonify({'error': 'Faltan datos requeridos'}), 400

    # Verificar si el usuario ya existe
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'error': 'El nombre de usuario ya existe'}), 400
    if User.query.filter_by(email=data['email']).first():
        return jsonify({'error': 'El email ya está registrado'}), 400

    try:
        # Crear organización
        org = Organization(
            name=data['organization_name'],
            sua_db_path=data['sua_db_path']
        )
        db.session.add(org)
        db.session.flush()  # Para obtener el ID de la organización

        # Crear usuario
        user = User(
            username=data['username'],
            email=data['email'],
            organization=org,
            is_admin=True
        )
        user.set_password(data['password'])
        db.session.add(user)
        db.session.commit()

        # Migrar datos del SUA
        migrator = SUAMigrator(data['sua_db_path'], org.id)
        success, message = migrator.migrate_all()

        if not success:
            return jsonify({'error': message}), 500
        else:
            return jsonify({
                'message': 'Usuario y organización registrados exitosamente. Datos del SUA migrados.',
                'user_id': user.id
            }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()

    if not all(k in data for k in ['username', 'password']):
        return jsonify({'error': 'Username y password son requeridos'}), 400

    user = User.query.filter_by(username=data['username']).first()

    if user and user.check_password(data['password']):
        access_token = create_access_token(identity={
            'user_id': user.id,
            'organization_id': user.organization_id
        })
        return jsonify({
            'access_token': access_token,
            'user_id': user.id,
            'organization_id': user.organization_id
        }), 200

    return jsonify({'error': 'Credenciales inválidas'}), 401

@bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    current_user_id = get_jwt_identity()['user_id']
    user = User.query.get(current_user_id)

    return jsonify({
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'organization_id': user.organization_id,
        'is_admin': user.is_admin
    }), 200

@bp.route('/test-sua-connection', methods=['POST'])
def test_sua_connection():
    data = request.get_json()

    if 'sua_db_path' not in data:
        return jsonify({'error': 'Se requiere sua_db_path'}), 400

    try:
        # Creamos una instancia temporal del migrador
        migrator = SUAMigrator(data['sua_db_path'], organization_id=1)

        # Usamos el nuevo método para obtener información de las tablas
        tables_info = migrator.get_sua_tables()

        # Contar tablas importantes
        important_tables_count = sum(1 for info in tables_info.values() if info['is_important'])

        # Contar filas totales
        total_rows = sum(info['row_count'] for info in tables_info.values())

        # Preparar la respuesta
        response = {
            'success': True,
            'message': 'Conexión exitosa',
            'summary': {
                'total_tables': len(tables_info),
                'important_tables': important_tables_count,
                'total_rows': total_rows
            },
            'tables': []
        }

        # Añadir información detallada de cada tabla
        for table_name, info in tables_info.items():
            response['tables'].append({
                'name': table_name,
                'row_count': info['row_count'],
                'is_important': info['is_important'],
                'columns': info['columns']
            })

        return jsonify(response), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error al conectar: {str(e)}'
        }), 500

@bp.route('/migrate-table', methods=['POST'])
def migrate_single_table():
    """Migra una tabla específica del SUA"""
    data = request.get_json()

    if 'sua_db_path' not in data:
        return jsonify({'error': 'Se requiere sua_db_path'}), 400

    if 'table_name' not in data:
        return jsonify({'error': 'Se requiere table_name'}), 400

    # Obtener el ID de la organización (usar 1 para pruebas)
    organization_id = data.get('organization_id', 1)

    try:
        # Creamos una instancia del migrador
        migrator = SUAMigrator(data['sua_db_path'], organization_id=organization_id)

        # Migrar la tabla específica
        success, message, rows = migrator.migrate_table(data['table_name'])

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'rows_processed': rows
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': message
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error en la migración: {str(e)}'
        }), 500

@bp.route('/test-full-migration', methods=['POST'])
def test_full_migration():
    """Migra todas las tablas seleccionadas del SUA"""
    data = request.get_json()

    if 'sua_db_path' not in data:
        return jsonify({'error': 'Se requiere sua_db_path'}), 400

    # Obtener tablas específicas a migrar (opcional)
    tables_to_include = data.get('tables', None)

    # Obtener el ID de la organización (usar 1 para pruebas)
    organization_id = data.get('organization_id', 1)

    try:
        # Creamos una instancia del migrador
        migrator = SUAMigrator(data['sua_db_path'], organization_id=organization_id)

        # Iniciamos la migración completa
        success, result = migrator.migrate_all(tables_to_include)

        if success:
            return jsonify({
                'success': True,
                'message': result['message'],
                'stats': result['stats'],
                'details': result['details']
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': result['message'],
                'stats': result.get('stats', {}),
                'error': result.get('message', 'Error desconocido')
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error en la migración: {str(e)}'
        }), 500



