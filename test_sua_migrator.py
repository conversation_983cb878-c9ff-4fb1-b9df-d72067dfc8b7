import unittest
import os
import sys
from unittest.mock import patch, MagicMock

# Add the project directory to the path so we can import the app
sys.path.append(os.path.abspath(os.path.dirname(__file__)))
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), 'toolsua-backend')))

# Import the app and SUAMigrator class
from app import create_app
from app.services.sua_migrator import SUAMigrator

class TestSUAMigrator(unittest.TestCase):
    """Test cases for the SUAMigrator class"""

    def setUp(self):
        """Setup before each test"""
        # Create a Flask app and application context
        self.app = create_app()
        self.app_context = self.app.app_context()
        self.app_context.push()

        # Create a mock for pyodbc.connect
        self.conn_mock = MagicMock()
        self.cursor_mock = MagicMock()
        self.conn_mock.cursor.return_value = self.cursor_mock

        # Path to a test database file
        self.test_db_path = "test_sua.mdb"
        self.test_org_id = 1

    def tearDown(self):
        """Teardown after each test"""
        # Pop the application context
        self.app_context.pop()

    @patch('pyodbc.connect')
    def test_init(self, mock_connect):
        """Test the initialization of SUAMigrator"""
        # Setup the mock
        mock_connect.return_value = self.conn_mock

        # Create an instance of SUAMigrator
        migrator = SUAMigrator(self.test_db_path, self.test_org_id)

        # Check that the attributes are set correctly
        self.assertEqual(migrator.sua_path, self.test_db_path)
        self.assertEqual(migrator.organization_id, self.test_org_id)
        self.assertIn(self.test_db_path, migrator.conn_str)
        self.assertIn("Microsoft Access Driver", migrator.conn_str)

    @patch('pyodbc.connect')
    @patch('app.services.sua_migrator.db')
    def test_discover_tables(self, mock_db, mock_connect):
        """Test the discover_tables method"""
        # Setup the connect mock
        mock_connect.return_value = self.conn_mock

        # Setup the cursor mock to return some tables
        table_mock = MagicMock()
        table_mock.table_name = "TestTable"
        self.cursor_mock.tables.return_value = [table_mock]

        # Setup the columns mock
        column_mock = MagicMock()
        column_mock.column_name = "TestColumn"
        column_mock.type_name = "TEXT"
        self.cursor_mock.columns.return_value = [column_mock]

        # Mock the db.engine
        mock_engine = MagicMock()
        mock_db.engine = mock_engine

        # Create an instance of SUAMigrator
        migrator = SUAMigrator(self.test_db_path, self.test_org_id)
        migrator.discover_tables()

        # Check that the methods were called correctly
        mock_connect.assert_called_once()
        self.cursor_mock.tables.assert_called_once()
        self.cursor_mock.columns.assert_called_once()

    @patch('pyodbc.connect')
    def test_map_access_to_sql_type(self, mock_connect):
        """Test the _map_access_to_sql_type method"""
        # Setup the mock
        mock_connect.return_value = self.conn_mock

        # Create an instance of SUAMigrator
        migrator = SUAMigrator(self.test_db_path, self.test_org_id)

        # Test mapping of various Access types
        from sqlalchemy import String, Integer, Float, DateTime, Boolean, Text

        self.assertEqual(migrator._map_access_to_sql_type("TEXT"), String)
        self.assertEqual(migrator._map_access_to_sql_type("VARCHAR"), String)
        self.assertEqual(migrator._map_access_to_sql_type("MEMO"), Text)
        self.assertEqual(migrator._map_access_to_sql_type("INTEGER"), Integer)
        self.assertEqual(migrator._map_access_to_sql_type("LONG"), Integer)
        self.assertEqual(migrator._map_access_to_sql_type("SINGLE"), Float)
        self.assertEqual(migrator._map_access_to_sql_type("DOUBLE"), Float)
        self.assertEqual(migrator._map_access_to_sql_type("DATETIME"), DateTime)
        self.assertEqual(migrator._map_access_to_sql_type("BIT"), Boolean)

        # Test with unknown type - should default to String
        self.assertEqual(migrator._map_access_to_sql_type("UNKNOWN_TYPE"), String)

    def test_migrate_all(self):
        """Test the migrate_all method with direct mocking"""
        # Create a mock SUAMigrator instance
        migrator = MagicMock()
        migrator.migrate_all.return_value = (True, "Migración completada exitosamente")

        # Call migrate_all
        success, message = migrator.migrate_all()

        # Check the results
        self.assertTrue(success)
        self.assertEqual(message, "Migración completada exitosamente")
        migrator.migrate_all.assert_called_once()

if __name__ == "__main__":
    unittest.main()
