import os
import sys
import json
import time
import argparse
from pprint import pprint

# Añadir el directorio del proyecto al path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Importar el SUAMigrator y la aplicación Flask
try:
    from toolsua_backend.app.services.sua_migrator import SUAMigrator, IMPORTANT_TABLES
    from toolsua_backend.app import create_app
except ImportError:
    # Intentar con guion
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'toolsua-backend'))
    from app.services.sua_migrator import SUAMigrator, IMPORTANT_TABLES
    from app import create_app

# Crear la aplicación Flask y el contexto de aplicación
app = create_app()
app_context = app.app_context()

def test_connection(sua_path):
    """Prueba la conexión a la base de datos SUA real"""
    print("\n=== Probando conexión a la base de datos SUA ===")

    # Verificar que el archivo existe
    if not os.path.exists(sua_path):
        print(f"ERROR: No se encontró la base de datos en {sua_path}")
        return False

    print(f"Base de datos encontrada en {sua_path}")

    # Usar el contexto de aplicación
    with app_context:
        try:
            # Crear una instancia del migrador en modo de prueba
            migrator = SUAMigrator(sua_path, organization_id=1, test_mode=True)

            # Obtener información sobre las tablas
            print("Obteniendo información de las tablas...")
            tables_info = migrator.get_sua_tables()

            # Mostrar un resumen
            total_tables = len(tables_info)
            important_tables = sum(1 for info in tables_info.values() if info['is_important'])
            total_rows = sum(info['row_count'] for info in tables_info.values())

            print(f"\nResumen:")
            print(f"- Total de tablas: {total_tables}")
            print(f"- Tablas importantes: {important_tables}")
            print(f"- Total de filas: {total_rows}")

            # Mostrar todas las tablas disponibles
            print("\nTablas disponibles:")
            for table_name, info in sorted(tables_info.items()):
                print(f"- {table_name}: {info['row_count']} filas")

            # Mostrar información detallada de las tablas importantes
            print("\nTablas importantes:")
            for table_name in IMPORTANT_TABLES:
                if table_name in tables_info:
                    info = tables_info[table_name]
                    print(f"- {table_name}: {info['row_count']} filas")
                    print(f"  Columnas: {len(info['columns'])}")

            return tables_info

        except Exception as e:
            print(f"ERROR: {str(e)}")
            return None

def test_migrate_table(sua_path, table_name):
    """Prueba la migración de una tabla específica"""
    print(f"\n=== Probando migración de la tabla {table_name} ===")

    # Usar el contexto de aplicación
    with app_context:
        try:
            # Crear una instancia del migrador en modo de prueba
            migrator = SUAMigrator(sua_path, organization_id=1, test_mode=True)

            # Migrar la tabla
            print(f"Migrando tabla {table_name}...")
            start_time = time.time()
            success, message, rows = migrator.migrate_table(table_name)
            elapsed_time = time.time() - start_time

            if success:
                print(f"Migración exitosa: {message}")
                print(f"Filas procesadas: {rows}")
                print(f"Tiempo transcurrido: {elapsed_time:.2f} segundos")
                print(f"Velocidad: {rows / elapsed_time:.2f} filas/segundo")
            else:
                print(f"Error en la migración: {message}")

            return success, rows, elapsed_time

        except Exception as e:
            print(f"ERROR: {str(e)}")
            return False, 0, 0

def test_migrate_all(sua_path, tables=None):
    """Prueba la migración de todas las tablas importantes"""
    print("\n=== Probando migración completa ===")

    # Usar el contexto de aplicación
    with app_context:
        try:
            # Crear una instancia del migrador en modo de prueba
            migrator = SUAMigrator(sua_path, organization_id=1, test_mode=True)

            # Migrar todas las tablas
            print("Iniciando migración completa...")
            start_time = time.time()
            success, result = migrator.migrate_all(tables)
            elapsed_time = time.time() - start_time

            if success:
                print(f"Migración exitosa: {result['message']}")
                print(f"Tablas procesadas: {result['stats']['tables_processed']}")
                print(f"Filas procesadas: {result['stats']['rows_processed']}")
                print(f"Tiempo transcurrido: {elapsed_time:.2f} segundos")

                # Mostrar detalles por tabla
                print("\nDetalles por tabla:")
                for detail in result['details']:
                    print(f"- {detail['table']}: {detail['rows']} filas")
            else:
                print(f"Error en la migración: {result['message']}")
                if 'stats' in result:
                    print(f"Tablas procesadas: {result['stats']['tables_processed']}")
                    print(f"Filas procesadas: {result['stats']['rows_processed']}")

                # Mostrar errores
                if 'stats' in result and 'errors' in result['stats']:
                    print("\nErrores:")
                    for error in result['stats']['errors']:
                        print(f"- {error['table']}: {error['error']}")

            return success, result

        except Exception as e:
            print(f"ERROR: {str(e)}")
            return False, None

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Prueba de migración de SUA')
    parser.add_argument('--path', default=r"C:\Cobranza\SUA\SUA.MDB", help='Ruta a la base de datos SUA')
    parser.add_argument('--table', help='Tabla específica a migrar')
    parser.add_argument('--all', action='store_true', help='Migrar todas las tablas importantes')
    parser.add_argument('--list', action='store_true', help='Listar tablas disponibles')

    args = parser.parse_args()

    # Verificar que el archivo existe
    if not os.path.exists(args.path):
        print(f"ERROR: No se encontró la base de datos en {args.path}")
        return 1

    # Listar tablas
    if args.list:
        tables_info = test_connection(args.path)
        if not tables_info:
            return 1
        return 0

    # Migrar una tabla específica
    if args.table:
        success, rows, elapsed_time = test_migrate_table(args.path, args.table)
        return 0 if success else 1

    # Migrar todas las tablas
    if args.all:
        success, result = test_migrate_all(args.path)
        return 0 if success else 1

    # Si no se especifica ninguna acción, mostrar la ayuda
    if not (args.list or args.table or args.all):
        tables_info = test_connection(args.path)
        if not tables_info:
            return 1

        # Preguntar qué tabla migrar
        print("\n¿Qué tabla deseas migrar?")
        print("1. PATRON (recomendado para prueba)")
        print("2. ASEGURADO")
        print("3. MOVIMIENTOS")
        print("4. CREDITOS")
        print("5. Todas las tablas importantes")
        print("0. Salir")

        choice = input("Selecciona una opción (0-5): ")

        if choice == "1":
            test_migrate_table(args.path, "PATRON")
        elif choice == "2":
            test_migrate_table(args.path, "ASEGURADO")
        elif choice == "3":
            test_migrate_table(args.path, "MOVIMIENTOS")
        elif choice == "4":
            test_migrate_table(args.path, "CREDITOS")
        elif choice == "5":
            test_migrate_all(args.path)
        else:
            print("Saliendo...")

    return 0

if __name__ == "__main__":
    sys.exit(main())
