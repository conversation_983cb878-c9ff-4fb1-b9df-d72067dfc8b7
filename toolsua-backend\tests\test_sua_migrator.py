import unittest
import os
import tempfile
import datetime
from unittest.mock import patch, MagicMock

# Importar el SUAMigrator
from app.services.sua_migrator import SUAMigrator, IMPORTANT_TABLES

class TestSUAMigrator(unittest.TestCase):
    """Pruebas unitarias para el SUAMigrator"""

    def setUp(self):
        """Configuración para cada prueba"""
        # Crear un archivo temporal para simular la base de datos SUA
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.mdb', delete=False)
        self.temp_db_path = self.temp_db.name
        self.temp_db.close()

        # ID de organización de prueba
        self.org_id = 999

        # Crear mocks para pyodbc
        self.conn_mock = MagicMock()
        self.cursor_mock = MagicMock()
        self.conn_mock.cursor.return_value = self.cursor_mock

    def tearDown(self):
        """Limpieza después de cada prueba"""
        # Eliminar el archivo temporal
        if os.path.exists(self.temp_db_path):
            os.unlink(self.temp_db_path)

    @patch('pyodbc.connect')
    def test_init(self, mock_connect):
        """Prueba la inicialización del SUAMigrator"""
        # Crear una instancia de SUAMigrator
        migrator = SUAMigrator(self.temp_db_path, self.org_id)

        # Verificar que los atributos se inicializan correctamente
        self.assertEqual(migrator.sua_path, self.temp_db_path)
        self.assertEqual(migrator.organization_id, self.org_id)
        self.assertIn(self.temp_db_path, migrator.conn_str)
        self.assertIn('Microsoft Access Driver', migrator.conn_str)
        self.assertIn('PWD=S5@N52V49', migrator.conn_str)

        # Verificar que las estadísticas se inicializan correctamente
        self.assertIn('started_at', migrator.migration_stats)
        self.assertEqual(migrator.migration_stats['tables_processed'], 0)
        self.assertEqual(migrator.migration_stats['rows_processed'], 0)
        self.assertEqual(migrator.migration_stats['errors'], [])

    @patch('pyodbc.connect')
    def test_get_sua_tables(self, mock_connect):
        """Prueba el método get_sua_tables"""
        # Configurar el mock para devolver tablas de prueba
        mock_connect.return_value = self.conn_mock

        # Configurar el cursor para devolver tablas de prueba
        table1 = MagicMock()
        table1.table_name = 'ASEGURADO'
        table2 = MagicMock()
        table2.table_name = 'PATRON'
        self.cursor_mock.tables.return_value = [table1, table2]

        # Configurar el cursor para devolver columnas de prueba
        col1 = MagicMock()
        col1.column_name = 'NSS'
        col1.type_name = 'TEXT'
        col1.nullable = False
        col2 = MagicMock()
        col2.column_name = 'NOMBRE'
        col2.type_name = 'TEXT'
        col2.nullable = True
        self.cursor_mock.columns.side_effect = [[col1, col2], [col1, col2]]

        # Configurar el cursor para devolver conteos de filas
        self.cursor_mock.fetchone.side_effect = [(100,), (50,)]

        # Crear una instancia de SUAMigrator
        migrator = SUAMigrator(self.temp_db_path, self.org_id)

        # Llamar al método get_sua_tables
        tables_info = migrator.get_sua_tables()

        # Verificar que se devuelve la información correcta
        self.assertEqual(len(tables_info), 2)
        self.assertIn('ASEGURADO', tables_info)
        self.assertIn('PATRON', tables_info)

        # Verificar la información de la tabla ASEGURADO
        asegurado_info = tables_info['ASEGURADO']
        self.assertEqual(len(asegurado_info['columns']), 2)
        self.assertEqual(asegurado_info['row_count'], 100)
        self.assertTrue(asegurado_info['is_important'])

        # Verificar la información de la tabla PATRON
        patron_info = tables_info['PATRON']
        self.assertEqual(len(patron_info['columns']), 2)
        self.assertEqual(patron_info['row_count'], 50)
        self.assertTrue(patron_info['is_important'])

        # Verificar que se llamó a los métodos correctos
        mock_connect.assert_called_once_with(migrator.conn_str)
        self.cursor_mock.tables.assert_called_once()
        self.assertEqual(self.cursor_mock.columns.call_count, 2)
        self.assertEqual(self.cursor_mock.execute.call_count, 2)

    def test_migrate_table(self):
        """Prueba el método migrate_table con mocks directos"""
        # Crear una instancia de SUAMigrator con mocks
        migrator = SUAMigrator(self.temp_db_path, self.org_id)

        # Reemplazar los métodos que acceden a la base de datos con mocks
        migrator.discover_tables = MagicMock()

        # Mock para la conexión a la base de datos
        with patch('pyodbc.connect') as mock_connect:
            # Configurar el mock para devolver una conexión
            mock_connect.return_value = self.conn_mock

            # Configurar el cursor para devolver datos de prueba
            col1 = MagicMock()
            col1.column_name = 'NSS'
            col2 = MagicMock()
            col2.column_name = 'NOMBRE'
            self.cursor_mock.description = [col1, col2]

            # Configurar fetchmany para devolver filas de prueba
            self.cursor_mock.fetchmany.side_effect = [
                [('12345678901', 'Juan Pérez'), ('98765432109', 'María López')],
                []  # Terminar el bucle
            ]

            # Mock para Base.metadata.tables
            migrator.Base.metadata = MagicMock()
            migrator.Base.metadata.tables = {
                'sua_asegurado': MagicMock()
            }

            # Mock para db.session
            with patch('app.services.sua_migrator.db') as mock_db:
                mock_session = MagicMock()
                mock_db.session = mock_session

                # Llamar al método migrate_table
                success, message, rows = migrator.migrate_table('ASEGURADO')

                # Verificar que se devuelve el resultado correcto
                self.assertTrue(success)
                self.assertIn('ASEGURADO', message)
                self.assertEqual(rows, 2)

                # Verificar que se llamó a los métodos correctos
                mock_connect.assert_called_once_with(migrator.conn_str)
                self.cursor_mock.execute.assert_called_once_with('SELECT * FROM [ASEGURADO]')
                self.cursor_mock.fetchmany.assert_called()
                mock_session.execute.assert_called_once()
                mock_session.commit.assert_called_once()

                # Verificar que se actualizaron las estadísticas
                self.assertEqual(migrator.migration_stats['tables_processed'], 1)
                self.assertEqual(migrator.migration_stats['rows_processed'], 2)

    def test_migrate_all(self):
        """Prueba el método migrate_all con mocks directos"""
        # Crear una instancia de SUAMigrator
        migrator = SUAMigrator(self.temp_db_path, self.org_id)

        # Crear mocks para los métodos que se llaman dentro de migrate_all
        migrator.discover_tables = MagicMock()
        migrator.migrate_table = MagicMock()

        # Configurar migrate_table para devolver resultados de prueba para diferentes tablas
        migrator.migrate_table.side_effect = [
            (True, "Migración de ASEGURADO completada: 100 filas", 100),
            (True, "Migración de PATRON completada: 50 filas", 50),
            (False, "Error al migrar tabla CREDITOS: Error de prueba", 0)
        ]

        # Llamar al método migrate_all con tablas específicas
        tables_to_include = ['ASEGURADO', 'PATRON', 'CREDITOS']
        success, result = migrator.migrate_all(tables_to_include)

        # Verificar que se devuelve el resultado correcto
        self.assertFalse(success)  # False porque una tabla falló
        self.assertIn('Migración parcial', result['message'])

        # Verificar que se llamó a los métodos correctos
        migrator.discover_tables.assert_called_once_with(tables_to_include)
        self.assertEqual(migrator.migrate_table.call_count, 3)

        # Verificar los detalles de las tablas
        details = result['details']
        self.assertEqual(len(details), 3)

        # Verificar que las estadísticas incluyen la duración
        self.assertIn('duration_seconds', result['stats'])
        self.assertIn('completed_at', result['stats'])

if __name__ == '__main__':
    unittest.main()
