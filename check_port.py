import socket
import sys

def check_port(host, port):
    """Verifica si un puerto está abierto en un host"""
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(2)
    result = sock.connect_ex((host, port))
    sock.close()
    
    if result == 0:
        print(f"El puerto {port} está abierto en {host}")
        return True
    else:
        print(f"El puerto {port} está cerrado en {host}")
        return False

if __name__ == "__main__":
    # Obtener la dirección IP local
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)
    
    print(f"Nombre del host: {hostname}")
    print(f"Dirección IP local: {local_ip}")
    
    # Verificar el puerto 5000 en localhost
    print("\nVerificando puerto 5000 en localhost (127.0.0.1):")
    check_port("127.0.0.1", 5000)
    
    # Verificar el puerto 5000 en la IP local
    print(f"\nVerificando puerto 5000 en la IP local ({local_ip}):")
    check_port(local_ip, 5000)
    
    # Si se proporciona una IP como argumento, verificarla también
    if len(sys.argv) > 1:
        ip = sys.argv[1]
        print(f"\nVerificando puerto 5000 en la IP proporcionada ({ip}):")
        check_port(ip, 5000)
