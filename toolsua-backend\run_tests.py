import unittest
import sys
import os

# Asegurarse de que el directorio actual esté en el path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def run_tests():
    """Ejecuta todas las pruebas"""
    # <PERSON><PERSON><PERSON>r y cargar todas las pruebas
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover('tests', pattern='test_*.py')
    
    # Ejecutar las pruebas
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Devolver código de salida basado en el resultado
    return 0 if result.wasSuccessful() else 1

if __name__ == '__main__':
    sys.exit(run_tests())
