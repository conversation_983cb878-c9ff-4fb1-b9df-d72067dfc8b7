import unittest
import json
import os
import tempfile
from unittest.mock import patch, MagicMock

from app import create_app
from app.services.sua_migrator import SUAMigrator

class TestAPIEndpoints(unittest.TestCase):
    """Pruebas para los endpoints de la API relacionados con la migración del SUA"""

    def setUp(self):
        """Configuración para cada prueba"""
        # Crear una aplicación de prueba
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()

        # Crear un contexto de aplicación
        self.app_context = self.app.app_context()
        self.app_context.push()

        # Crear un archivo temporal para simular la base de datos SUA
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.mdb', delete=False)
        self.temp_db_path = self.temp_db.name
        self.temp_db.close()

    def tearDown(self):
        """Limpieza después de cada prueba"""
        # Eliminar el archivo temporal
        if os.path.exists(self.temp_db_path):
            os.unlink(self.temp_db_path)

        # Eliminar el contexto de aplicación
        self.app_context.pop()

    @patch('app.auth.SUAMigrator')
    def test_test_sua_connection(self, mock_sua_migrator):
        """Prueba el endpoint /api/auth/test-sua-connection"""
        # Configurar el mock para devolver información de tablas
        mock_instance = MagicMock()
        mock_sua_migrator.return_value = mock_instance

        # Configurar get_sua_tables para devolver información de prueba
        mock_instance.get_sua_tables.return_value = {
            'ASEGURADO': {
                'columns': [{'name': 'NSS', 'type': 'TEXT', 'nullable': False}],
                'row_count': 100,
                'is_important': True
            },
            'PATRON': {
                'columns': [{'name': 'REG_PAT', 'type': 'TEXT', 'nullable': False}],
                'row_count': 50,
                'is_important': True
            }
        }

        # Hacer una solicitud POST al endpoint
        response = self.client.post(
            '/api/auth/test-sua-connection',
            json={'sua_db_path': self.temp_db_path}
        )

        # Verificar la respuesta
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['summary']['total_tables'], 2)
        self.assertEqual(data['summary']['important_tables'], 2)
        self.assertEqual(data['summary']['total_rows'], 150)
        self.assertEqual(len(data['tables']), 2)

        # Verificar que se llamó al método correcto
        mock_sua_migrator.assert_called_once_with(self.temp_db_path, organization_id=1)
        mock_instance.get_sua_tables.assert_called_once()

    @patch('app.auth.SUAMigrator')
    def test_migrate_table(self, mock_sua_migrator):
        """Prueba el endpoint /api/auth/migrate-table"""
        # Configurar el mock para devolver resultados de migración
        mock_instance = MagicMock()
        mock_sua_migrator.return_value = mock_instance

        # Configurar migrate_table para devolver resultados de prueba
        mock_instance.migrate_table.return_value = (
            True, "Migración de ASEGURADO completada: 100 filas", 100
        )

        # Hacer una solicitud POST al endpoint
        response = self.client.post(
            '/api/auth/migrate-table',
            json={
                'sua_db_path': self.temp_db_path,
                'table_name': 'ASEGURADO',
                'organization_id': 999
            }
        )

        # Verificar la respuesta
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('ASEGURADO', data['message'])
        self.assertEqual(data['rows_processed'], 100)

        # Verificar que se llamó al método correcto
        mock_sua_migrator.assert_called_once_with(self.temp_db_path, organization_id=999)
        mock_instance.migrate_table.assert_called_once_with('ASEGURADO')

    @patch('app.auth.SUAMigrator')
    def test_test_full_migration(self, mock_sua_migrator):
        """Prueba el endpoint /api/auth/test-full-migration"""
        # Configurar el mock para devolver resultados de migración
        mock_instance = MagicMock()
        mock_sua_migrator.return_value = mock_instance

        # Configurar migrate_all para devolver resultados de prueba
        mock_instance.migrate_all.return_value = (
            True, {
                'message': "Migración completada exitosamente",
                'stats': {
                    'started_at': '2023-01-01T00:00:00',
                    'completed_at': '2023-01-01T00:01:00',
                    'duration_seconds': 60,
                    'tables_processed': 2,
                    'rows_processed': 150,
                    'errors': []
                },
                'details': [
                    {'table': 'ASEGURADO', 'success': True, 'message': 'Migración completada', 'rows': 100},
                    {'table': 'PATRON', 'success': True, 'message': 'Migración completada', 'rows': 50}
                ]
            }
        )

        # Hacer una solicitud POST al endpoint
        response = self.client.post(
            '/api/auth/test-full-migration',
            json={
                'sua_db_path': self.temp_db_path,
                'tables': ['ASEGURADO', 'PATRON'],
                'organization_id': 999
            }
        )

        # Verificar la respuesta
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['message'], "Migración completada exitosamente")
        self.assertEqual(data['stats']['tables_processed'], 2)
        self.assertEqual(data['stats']['rows_processed'], 150)
        self.assertEqual(len(data['details']), 2)

        # Verificar que se llamó al método correcto
        mock_sua_migrator.assert_called_once_with(self.temp_db_path, organization_id=999)
        mock_instance.migrate_all.assert_called_once_with(['ASEGURADO', 'PATRON'])

    def test_missing_sua_db_path(self):
        """Prueba los endpoints cuando falta sua_db_path"""
        # Probar test-sua-connection
        response = self.client.post('/api/auth/test-sua-connection', json={})
        self.assertEqual(response.status_code, 400)

        # Probar migrate-table
        response = self.client.post('/api/auth/migrate-table', json={})
        self.assertEqual(response.status_code, 400)

        # Probar test-full-migration
        response = self.client.post('/api/auth/test-full-migration', json={})
        self.assertEqual(response.status_code, 400)

    def test_missing_table_name(self):
        """Prueba el endpoint migrate-table cuando falta table_name"""
        response = self.client.post(
            '/api/auth/migrate-table',
            json={'sua_db_path': self.temp_db_path}
        )
        self.assertEqual(response.status_code, 400)

if __name__ == '__main__':
    unittest.main()
