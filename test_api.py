import unittest
import requests
import json
import os
import sys
import time

# Add the project directory to the path so we can import the app
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# Base URL for the API
BASE_URL = "http://127.0.0.1:5000"

class TestAPI(unittest.TestCase):
    """Test cases for the ToolSUA API"""

    def setUp(self):
        """Setup before each test - check if the server is running"""
        try:
            # Try to connect to the server
            requests.get(f"{BASE_URL}", timeout=2)
            print("Server is running")
        except requests.exceptions.ConnectionError:
            print("WARNING: Server does not appear to be running!")
            print("Start the server with: cd toolsua-backend && python run.py")

    def test_health_endpoint(self):
        """Test that the API health endpoint is working"""
        try:
            response = requests.get(f"{BASE_URL}/test", timeout=5)
            self.assertEqual(response.status_code, 200)
            data = response.json()
            self.assertEqual(data["message"], "API funcionando correctamente")
            print("✓ Health endpoint test passed")
        except requests.exceptions.RequestException as e:
            self.fail(f"Request failed: {e}")

    def test_auth_endpoints_exist(self):
        """Test that the auth endpoints exist (without valid credentials)"""
        try:
            # Test login endpoint
            response = requests.post(
                f"{BASE_URL}/api/auth/login",
                json={"username": "test_user", "password": "invalid_password"},
                timeout=5
            )
            # Should return 401 for invalid credentials, not 404
            self.assertEqual(response.status_code, 401)
            print("✓ Login endpoint test passed")

            # Test register endpoint (just checking it exists)
            response = requests.post(
                f"{BASE_URL}/api/auth/register",
                json={},
                timeout=5
            )
            # Should return 400 for missing data, not 404
            self.assertEqual(response.status_code, 400)
            print("✓ Register endpoint test passed")
        except requests.exceptions.RequestException as e:
            self.fail(f"Request failed: {e}")

    def test_sua_connection_endpoint(self):
        """Test the SUA connection test endpoint"""
        try:
            # Test with invalid path
            response = requests.post(
                f"{BASE_URL}/api/auth/test-sua-connection",
                json={"sua_db_path": "invalid_path.mdb"},
                timeout=5
            )
            # Should return 500 for invalid path
            self.assertEqual(response.status_code, 500)
            data = response.json()
            self.assertIn("error", data)
            print("✓ SUA connection test with invalid path passed")
        except requests.exceptions.RequestException as e:
            self.fail(f"Request failed: {e}")

    def test_missing_sua_path(self):
        """Test the SUA connection test endpoint with missing path"""
        try:
            # Test with missing path
            response = requests.post(
                f"{BASE_URL}/api/auth/test-sua-connection",
                json={},
                timeout=5
            )
            # Should return 400 for missing path
            self.assertEqual(response.status_code, 400)
            data = response.json()
            self.assertIn("error", data)
            print("✓ SUA connection test with missing path passed")
        except requests.exceptions.RequestException as e:
            self.fail(f"Request failed: {e}")

    def test_full_migration_endpoint(self):
        """Test the full migration endpoint"""
        try:
            # Test with invalid path
            response = requests.post(
                f"{BASE_URL}/api/auth/test-full-migration",
                json={"sua_db_path": "invalid_path.mdb"},
                timeout=5
            )
            # Should return 500 for invalid path
            self.assertEqual(response.status_code, 500)
            data = response.json()
            self.assertIn("error", data)
            print("✓ Full migration test with invalid path passed")
        except requests.exceptions.RequestException as e:
            self.fail(f"Request failed: {e}")

if __name__ == "__main__":
    # First start the Flask app in a separate process
    print("Make sure the Flask app is running on http://127.0.0.1:5000")
    print("You can start it with: cd toolsua-backend && python run.py")
    print("Waiting 3 seconds for the server to start...")
    time.sleep(3)

    # Run the tests
    unittest.main()
