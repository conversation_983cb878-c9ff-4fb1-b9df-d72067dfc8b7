import os
import sys
import json
from pprint import pprint

# Añadir el directorio del proyecto al path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'toolsua-backend')))

# Importar el SUAMigrator
from app.services.sua_migrator import SUAMigrator, IMPORTANT_TABLES

def test_connection():
    """Prueba la conexión a la base de datos SUA real"""
    print("\n=== Probando conexión a la base de datos SUA ===")
    
    # Ruta a la base de datos SUA real
    sua_path = r"C:\Cobranza\SUA\SUA.MDB"
    
    # Verificar que el archivo existe
    if not os.path.exists(sua_path):
        print(f"ERROR: No se encontró la base de datos en {sua_path}")
        return False
    
    print(f"Base de datos encontrada en {sua_path}")
    
    try:
        # Crear una instancia del migrador
        migrator = SUAMigrator(sua_path, organization_id=1)
        
        # Obtener información sobre las tablas
        print("Obteniendo información de las tablas...")
        tables_info = migrator.get_sua_tables()
        
        # Mostrar un resumen
        total_tables = len(tables_info)
        important_tables = sum(1 for info in tables_info.values() if info['is_important'])
        total_rows = sum(info['row_count'] for info in tables_info.values())
        
        print(f"\nResumen:")
        print(f"- Total de tablas: {total_tables}")
        print(f"- Tablas importantes: {important_tables}")
        print(f"- Total de filas: {total_rows}")
        
        # Mostrar información detallada de las tablas importantes
        print("\nTablas importantes:")
        for table_name in IMPORTANT_TABLES:
            if table_name in tables_info:
                info = tables_info[table_name]
                print(f"- {table_name}: {info['row_count']} filas")
                print(f"  Columnas: {len(info['columns'])}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: {str(e)}")
        return False

def test_migrate_table(table_name):
    """Prueba la migración de una tabla específica"""
    print(f"\n=== Probando migración de la tabla {table_name} ===")
    
    # Ruta a la base de datos SUA real
    sua_path = r"C:\Cobranza\SUA\SUA.MDB"
    
    try:
        # Crear una instancia del migrador
        migrator = SUAMigrator(sua_path, organization_id=1)
        
        # Migrar la tabla
        print(f"Migrando tabla {table_name}...")
        success, message, rows = migrator.migrate_table(table_name)
        
        if success:
            print(f"Migración exitosa: {message}")
            print(f"Filas procesadas: {rows}")
        else:
            print(f"Error en la migración: {message}")
        
        return success
        
    except Exception as e:
        print(f"ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    # Probar la conexión
    if test_connection():
        # Si la conexión es exitosa, probar la migración de una tabla
        # Elegir una tabla pequeña para la prueba
        test_migrate_table("PATRON")
    else:
        print("No se pudo conectar a la base de datos SUA. No se realizarán pruebas de migración.")
