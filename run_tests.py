import os
import sys
import subprocess
import time
import unittest
import requests

def wait_for_server(url, timeout=10):
    """Wait for the server to be available"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=1)
            if response.status_code == 200:
                return True
        except requests.exceptions.RequestException:
            time.sleep(0.5)
    return False

def run_tests():
    """Run all the tests in the project"""
    # Run the unit tests first (they don't need the Flask app)
    print("\n=== Running SUAMigrator unit tests ===")
    unittest.TextTestRunner().run(unittest.defaultTestLoader.loadTestsFromName('test_sua_migrator'))

    # Start the Flask app in a separate process
    print("\nStarting Flask application...")
    flask_process = subprocess.Popen(
        ["python", "toolsua-backend/run.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )

    # Wait for the Flask app to start
    print("Waiting for Flask app to start...")
    server_ready = wait_for_server("http://127.0.0.1:5000/test")

    if not server_ready:
        print("ERROR: Flask application failed to start within the timeout period.")
        flask_process.terminate()
        flask_process.wait()
        return

    print("Flask application is running!")

    try:
        # Run the API tests
        print("\n=== Running API tests ===")
        unittest.TextTestRunner().run(unittest.defaultTestLoader.loadTestsFromName('test_api'))

    finally:
        # Terminate the Flask app
        print("\nShutting down Flask application...")
        flask_process.terminate()
        flask_process.wait()

if __name__ == "__main__":
    run_tests()
