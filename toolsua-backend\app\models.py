from app import db
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

class Organization(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(120), nullable=False)
    sua_db_path = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    users = db.relationship('User', backref='organization', lazy=True)
    patrones = db.relationship('Patron', backref='organization', lazy=True)
    asegurados = db.relationship('Asegurado', backref='organization', lazy=True)

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_admin = db.Column(db.Boolean, default=False)
    organization_id = db.Column(db.Integer, db.ForeignKey('organization.id'), nullable=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Patron(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    reg_pat = db.Column(db.String(11), nullable=False)
    organization_id = db.Column(db.Integer, db.ForeignKey('organization.id'), nullable=False)
    rfc_pat = db.Column(db.String(13))
    nom_pat = db.Column(db.String(120))
    prima_rt = db.Column(db.Float)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (
        db.UniqueConstraint('reg_pat', 'organization_id', name='uix_reg_pat_org'),
    )

class Asegurado(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nss = db.Column(db.String(11), nullable=False)
    organization_id = db.Column(db.Integer, db.ForeignKey('organization.id'), nullable=False)
    reg_pat = db.Column(db.String(11), nullable=False)
    nombre = db.Column(db.String(50))
    apellido_paterno = db.Column(db.String(50))
    apellido_materno = db.Column(db.String(50))
    sal_ant1 = db.Column(db.Float)
    sal_ant2 = db.Column(db.Float)
    sal_ant3 = db.Column(db.Float)
    num_cre = db.Column(db.String(10))
    tip_des = db.Column(db.String(20))
    val_des = db.Column(db.Float)
    tab_dism = db.Column(db.String(1))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    __table_args__ = (
        db.UniqueConstraint('nss', 'organization_id', name='uix_nss_org'),
        db.ForeignKeyConstraint(
            ['reg_pat', 'organization_id'],
            ['patron.reg_pat', 'patron.organization_id'],
            name='fk_asegurado_patron'
        )
    )

