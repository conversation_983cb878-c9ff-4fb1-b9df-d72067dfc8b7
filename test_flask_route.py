import requests
import time
import sys

def test_route(url, max_retries=5, retry_delay=2):
    """Prueba una ruta específica con reintentos"""
    print(f"Probando la ruta: {url}")
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=5)
            print(f"Intento {attempt + 1}: <PERSON>ódigo de estado {response.status_code}")
            
            if response.status_code == 200:
                print(f"Respuesta: {response.json()}")
                return True
            else:
                print(f"Error: {response.text}")
        except requests.exceptions.RequestException as e:
            print(f"Intento {attempt + 1}: Error de conexión: {e}")
        
        if attempt < max_retries - 1:
            print(f"Reintentando en {retry_delay} segundos...")
            time.sleep(retry_delay)
    
    return False

if __name__ == "__main__":
    base_url = "http://127.0.0.1:5000"
    
    # Si se proporciona una URL como argumento, usarla en su lugar
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    # Probar la ruta /test
    test_route(f"{base_url}/test")
